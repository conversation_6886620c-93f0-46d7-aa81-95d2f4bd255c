import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON>, Badge, message } from 'antd';
import { AudioOutlined, LoadingOutlined } from '@ant-design/icons';
import { speechRecognitionService, type CountdownInfo } from '../services/speechService';

interface SpeechRecognitionButtonProps {
  onTranscript: (text: string) => void;
  onInterimResult?: (text: string, isReplace?: boolean) => void; // 新增：临时结果回调，包含是否替换信息
  onStartListening?: () => void; // 新增：开始监听回调
  onEndListening?: () => void; // 新增：结束监听回调
  className?: string;
}

const SpeechRecognitionButton: React.FC<SpeechRecognitionButtonProps> = ({
  onTranscript,
  onInterimResult,
  onStartListening,
  onEndListening,
  className = ''
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isPreparing, setIsPreparing] = useState(false);
  const [countdownInfo, setCountdownInfo] = useState<CountdownInfo | null>(null);
  const [hasAudioDetected, setHasAudioDetected] = useState(false); // 改名：检测到音频数据（而不是语音识别结果）
  const [audioDetectionTimeout, setAudioDetectionTimeout] = useState<NodeJS.Timeout | null>(null); // 音频检测超时

  useEffect(() => {
    setIsSupported(speechRecognitionService.isRecognitionSupported());
  }, []);

  // 清理超时
  useEffect(() => {
    return () => {
      if (audioDetectionTimeout) {
        clearTimeout(audioDetectionTimeout);
      }
    };
  }, [audioDetectionTimeout]);

  const handleStartListening = async () => {
    if (isListening || isPreparing) {
      // 如果正在监听或准备中，点击停止
      speechRecognitionService.stopListening();
      setIsListening(false);
      setIsPreparing(false);
      setCountdownInfo(null);
      setHasAudioDetected(false);
      // 清除音频检测超时
      if (audioDetectionTimeout) {
        clearTimeout(audioDetectionTimeout);
        setAudioDetectionTimeout(null);
      }
      return;
    }

    try {
      await speechRecognitionService.startListening(
        (result) => {
          if (result.isFinal) {
            // 最终结果 - 调用onTranscript回调
            onTranscript(result.transcript);
          } else {
            // 临时结果 - 立即调用onInterimResult回调，用于增量显示
            onInterimResult?.(result.transcript, result.isReplace);
          }
        },
        (error) => {
          message.error(error);
          setIsListening(false);
          setIsPreparing(false);
          setCountdownInfo(null);
          setHasAudioDetected(false);
          // 清除音频检测超时
          if (audioDetectionTimeout) {
            clearTimeout(audioDetectionTimeout);
            setAudioDetectionTimeout(null);
          }
        },
        () => {
          // 识别结束
          setIsListening(false);
          setIsPreparing(false);
          setCountdownInfo(null);
          setHasAudioDetected(false);
          // 清除音频检测超时
          if (audioDetectionTimeout) {
            clearTimeout(audioDetectionTimeout);
            setAudioDetectionTimeout(null);
          }
          onEndListening?.(); // 通知外部识别结束，但不清理状态
        },
        () => {
          // 准备中状态
          setIsPreparing(true);
          setIsListening(false);
          setCountdownInfo(null);
          setHasAudioDetected(false);
        },
        () => {
          // 开始监听状态
          setIsPreparing(false);
          setIsListening(true);
          setHasAudioDetected(false); // 开始监听时重置音频检测状态
          onStartListening?.(); // 通知外部开始监听
        },
        {
          language: 'zh-CN',
          continuous: true,
          interimResults: true,
          noSpeechTimeout: 5000 // 5秒无语音超时
        },
        (countdown) => {
          // 倒计时回调 - 只有在没有检测到音频时才显示倒计时
          setCountdownInfo(countdown);
          // 当开始倒计时时，说明没有检测到音频，重置音频检测状态
          if (countdown) {
            setHasAudioDetected(false);
          }
        },
        () => {
          // 音频数据检测回调 - 检测到音频数据时立即触发
          setHasAudioDetected(true);

          // 清除之前的超时
          if (audioDetectionTimeout) {
            clearTimeout(audioDetectionTimeout);
          }

          // 设置新的超时，2秒后如果没有新的音频信号就重置状态
          const timeout = setTimeout(() => {
            setHasAudioDetected(false);
          }, 2000);
          setAudioDetectionTimeout(timeout);

          // 如果正在倒计时，清除倒计时（表示检测到了有效的音频输入）
          if (countdownInfo) {
            setCountdownInfo(null);
          }
        }
      );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      message.error('启动语音识别失败');
      setIsListening(false);
      setIsPreparing(false);
      setCountdownInfo(null);
      setHasAudioDetected(false);
      // 清除音频检测超时
      if (audioDetectionTimeout) {
        clearTimeout(audioDetectionTimeout);
        setAudioDetectionTimeout(null);
      }
    }
  };

  // 获取状态文本
  const getStatusText = () => {
    if (isPreparing) {
      return '准备中';
    }
    if (isListening) {
      // 如果有倒计时信息，优先显示倒计时（不管是否检测到音频）
      if (countdownInfo) {
        return (
          <span>
            识别中：没有检测到声音，将在
            <strong style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
              {countdownInfo.remainingSeconds}
            </strong>
            秒后结束！
          </span>
        );
      }
      // 只显示识别状态，不显示临时文字
      return '识别中';
    }
    return '';
  };

  // 获取tooltip文本
  const getTooltipText = () => {
    if (isListening || isPreparing) {
      return '结束语音输入';
    }
    return '语音输入';
  };

  // 动态生成按钮类名
  const getButtonClass = () => {
    let buttonClass = `scene-function-button speech-recognition-button ${className}`;
    
    if (isPreparing) {
      buttonClass += ' preparing';
    } else if (isListening) {
      buttonClass += ' listening';
    }
    
    return buttonClass;
  };

  if (!isSupported) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Tooltip title="浏览器不支持语音识别">
          <Button 
            className={`scene-function-button ${className}`}
            disabled
            icon={<AudioOutlined style={{ color: '#cccccc' }} />}
          />
        </Tooltip>
      </div>
    );
  }

  const statusText = getStatusText();

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Tooltip title={getTooltipText()}>
        <Badge
          dot={isListening && hasAudioDetected && !countdownInfo}
          color="#52c41a"
        >
          <Button
            className={getButtonClass()}
            onClick={handleStartListening}
            icon={
              isPreparing ? (
                <LoadingOutlined />
              ) : (
                <AudioOutlined />
              )
            }
          />
        </Badge>
      </Tooltip>
      {statusText && (
        <div style={{
          fontSize: '12px',
          color: '#666',
          whiteSpace: 'nowrap',
          maxWidth: countdownInfo ? '300px' : '120px', // 倒计时时增加宽度，否则使用默认宽度
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }}>
          {statusText}
        </div>
      )}
    </div>
  );
};

export default SpeechRecognitionButton;